:root {
  /* example of a standard key: "W" Key on the KB */
  --standard-key-size: 5rem; /* width & height */
}

/* ------------------ BACKGROUND ------------------ */

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-image: var(--color-bg);
}

/* ------------------ HEADER ------------------ */

.intro {
  max-width: 130rem;
  margin: 0 auto;
  padding: 4.8rem 3.2rem 0rem;
}

.title span {
  background-image: var(--color-bg-title);

  /* Use the text as a mask for the background. */
  /* This will show the gradient as a text color rather than element bg. */
  background-clip: text;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
}

.theme-and-layout {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  max-width: 120rem;
  margin: 0 auto 2.4rem;
  transition: all 0.25s;
}

.toggle-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle-section span {
  font-size: 2.4rem;
  cursor: pointer;
}

/* slider styling */

.slider-container {
  width: 80%;
  margin: 1.6rem auto;
  text-align: center;
}

.slider {
  width: 100%;
  transform: translateY(1.8rem);
}

/* Styling the track */
input[type='range']::-webkit-slider-runnable-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #555555, #f1f3f5);
}

/* Styling the thumb */
input[type='range']::-webkit-slider-thumb {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: #555; /* Thumb color */
  cursor: pointer;
  -webkit-appearance: none;
  transition: all 0.3s ease; /* Smooth transition */
  transform: translateY(-0.4rem);
}

/* Highlighting the thumb on hover */
input[type='range']::-webkit-slider-thumb:hover {
  box-shadow: 0 0 10px rgb(84, 84, 84, 0.4); /* Add a subtle box-shadow on hover */
  transform: translateY(-0.4rem) scale(1.2);
}

.slider-value {
  font-size: 2.4em;
  padding: 4px 8px;
  border-radius: 4px;
  color: #555; /* Match the thumb color */
  background: #fff; /* Background color for the value */
  box-shadow: 0 0 10px rgb(84, 84, 84, 0.1); /* Add a subtle box-shadow to the value */
}

.theme-section {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 2rem;
}

.theme-section > div {
  display: flex;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
}

.theme-color {
  width: 25%;
}

/* retro theme */
.retro div:nth-child(1) {
  background-color: #3c3e4a;
}

.retro div:nth-child(2) {
  background-color: #f8f9fa;
}

.retro div:nth-child(3) {
  background-color: #a4a9b0;
}

.retro div:nth-child(4) {
  background-color: #2f3674;
}

/* navy-blue */
.navy-blue div:nth-child(1) {
  background-color: #2a309c;
}

.navy-blue div:nth-child(2) {
  background-color: #3d489b;
}

.navy-blue div:nth-child(3) {
  background-color: #a4a9b0;
}

.navy-blue div:nth-child(4) {
  background-color: #2f3674;
}

/* ------------------ KEYBOARD SECTION ------------------ */

.keyboard {
  display: grid;
  margin: 0 auto;
  padding: 0.8rem;
  border-radius: 0.4rem;
  background-color: var(--color-keyboard);
  box-shadow: 0 0.4rem 0 0.6rem var(--color-keyboard-border),
    0 1.2rem 3.2rem rgba(0, 0, 0, 0.1);

  /* force GPU acceleration to reduce
   potential subrendering issues */
  transform: translateZ(0);

  transition: all 0.2s;
}

.full-size {
  grid-template-columns: 215fr 2fr 45fr 1fr 60fr;
  max-width: 122rem;
}

.tkl {
  /* whitespace in the tkl layout increased 
  to 7fr for better region seperation */
  grid-template-columns: 215fr 7fr 45fr;
  max-width: 100rem;
}

.seventy-five-percent {
  grid-template-columns: 79rem 0 16.5rem;
  max-width: 86rem;
}

.key {
  width: 100%;
  height: var(--standard-key-size);
  display: flex;
  justify-content: center;
  align-items: center;

  border-radius: var(--key-border-radius);
  font-size: 1.2rem;
  font-weight: 700;

  color: var(--color-keycaps-legends);
  background-color: var(--color-keycaps-bg);
  box-shadow: var(--box-shadow-keycaps);
  cursor: pointer;

  transition: all 0.075;
}

.key--accent-color {
  color: var(--color-keycaps-legends-accent);
  background-color: var(--color-keycaps-bg-accent);
  box-shadow: var(--box-shadow-keycaps-accent);
}

/* making specificity higher by adding .key */
.key.key--pressed {
  color: var(--color-keycaps-legends-pressed);
  background-color: var(--color-keycaps-bg-pressed);
  box-shadow: var(--box-shadow-keycaps-pressed);
}

/* key pressing simulation class is for using in js */
.key:hover,
.key--accent-color:hover,
.key--pressed:hover,
.key-pressing-simulation {
  transform: scale(0.98);
  box-shadow: var(--box-shadow-keycaps-hover);
}

.key--accent-color:hover {
  box-shadow: var(--box-shadow-keycaps-accent-hover);
}

.key--pressed:hover,
.key-pressing-simulation {
  box-shadow: var(--box-shadow-keycaps-pressed-hover);
}

.key.key-pressing-simulation {
  color: var(--color-keycaps-legends-pressing);
}

/* __________________________________
   ||||||||||||||||||||||||||||||||||
   |||| GENERAL KB LAYOUT STYLES ||||
   ||||||||||||||||||||||||||||||||||
*/

.region {
  padding: 0.5rem;
  gap: 0.25rem;
}

/* contains "ESC" & Function (F1-F12) */
.function {
  display: grid;
  grid-template-columns: 2fr 2fr repeat(4, 2fr) 1fr repeat(4, 2fr) 1fr repeat(
      4,
      2fr
    );
}

/* contains Top-Located Control (Prt Sc|Scr lck|Pause) keys  */
.system-control {
  grid-column: 3;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

/* contains Modifiers/Control (Ctrl|Alt|etc.) & Alpha (A-Z) keys */
.typewriter {
  grid-row: 2;
  display: grid;
}

.first-row,
.second-row,
.third-row,
.fourth-row,
.fifth-row {
  display: grid;
  justify-items: center;
  gap: 0.25rem;
}

.first-row {
  grid-template-columns: repeat(13, 1fr) 2fr;
}

.second-row {
  grid-template-columns: 1.5fr repeat(12, 1fr) 1.5fr;
}

.third-row {
  grid-template-columns: 1.79fr repeat(11, 1fr) 2.29fr;
}

.fourth-row {
  grid-template-columns: 2.29fr repeat(10, 1fr) 2.79fr;
}

.fifth-row {
  grid-template-columns: repeat(3, 1.29fr) 6.36fr repeat(4, 1.29fr);
}

/* contains Navigation (PgUp|PgDn|etc.) & Arrow keys */
.navigation {
  grid-column: 3;

  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(5, 1fr);
}

/* contains Numpad Keys */
.numpad {
  grid-column: 5;

  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(5, 1fr);
}

/* _______________________________
   |||||||||||||||||||||||||||||||
   |||| KEY SPECIFIC STYLES ||||||
   |||||||||||||||||||||||||||||||
*/

/**************************/
/* TYPEWRITER REGION KEYS */
/**************************/

/* styling keys with sublegends */
.key--sublegend {
  display: grid;
  grid-template-rows: 1fr auto auto 1fr;
  padding-top: 0.8rem;
  gap: 0.1rem;
}

.key--sublegend span:nth-child(odd),
.key--sublegend span:nth-child(even) {
  width: 100%;
  text-align: center;
  grid-row: 1;
}

.key--sublegend span:nth-child(even) {
  grid-row: 2;
}

/* styling enter key as return sign  */
.enter span {
  transform: translateY(-0.25rem);
  font-size: 2.8rem;
}

/* windows and context menu icons */
.metaleft svg {
  width: 1.8rem;
  height: 1.8rem;
  fill: var(--color-keycaps-legends);
}

.metaleft .key--accent svg {
  fill: var(--color-keycaps-legends-accent);
}

.metaleft.key--pressed svg {
  fill: var(--color-keycaps-legends-pressed);
}

.metaleft.key-pressing-simulation svg {
  fill: var(--color-keycaps-legends-pressing);
}

.metaright svg {
  width: 1.8rem;
  height: 1.8rem;
  fill: var(--color-keycaps-legends);
}

.metaright .key--accent svg {
  fill: var(--color-keycaps-legends-accent);
}

.metaright.key--pressed svg {
  fill: var(--color-keycaps-legends-pressed);
}

.metalright.key-pressing-simulation svg {
  fill: var(--color-keycaps-legends-pressing);
}

.contextmenu svg {
  width: 2rem;
  height: 2rem;
  stroke: var(--color-keycaps-legends);
  fill: none;
}

.contextmenu .key--accent svg {
  stroke: var(--color-keycaps-legends-accent);
}

.contextmenu.key--pressed svg {
  stroke: var(--color-keycaps-legends-pressed);
}

.contextmenu.key-pressing-simulation svg {
  stroke: var(--color-keycaps-legends-pressing);
}

/* spacebar legend */
.space span {
  transform: translateY(-1.6rem);
  font-size: 1.6rem;
}

/* side keys text aligning */
.backquote,
.tab,
.capslock,
.shiftleft,
.controlleft {
  justify-content: left;
  padding-left: 1.6rem;
}

.backspace,
.enter,
.shiftright,
.controlright {
  justify-content: right;
  padding-right: 1.6rem;
}

/**************************/
/* NAVIGATION REGION KEYS */
/**************************/

.arrowup {
  grid-column: 2;
  grid-row: 4;
}

.arrowleft,
.arrowdown,
.arrowright {
  grid-row: 5;
}

.arrowup svg,
.arrowleft svg,
.arrowdown svg,
.arrowright svg {
  width: 1.2rem;
  height: 1.2rem;
  fill: var(--color-keycaps-legends);
}

.arrowup.key--pressed svg,
.arrowleft.key--pressed svg,
.arrowdown.key--pressed svg,
.arrowright.key--pressed svg {
  fill: var(--color-keycaps-legends-pressed);
}

.arrowup.key-pressing-simulation svg,
.arrowleft.key-pressing-simulation svg,
.arrowdown.key-pressing-simulation svg,
.arrowright.key-pressing-simulation svg {
  fill: var(--color-keycaps-legends-pressing);
}

/**********************/
/* NUMPAD REGION KEYS */
/**********************/

.numpadadd {
  grid-column: 4;
  grid-row: 2 / span 2;
  height: 100%;
}

.numpadenter {
  grid-column: 4;
  grid-row: 4 / span 2;
  height: 100%;
}
.numpad0 {
  grid-column: 1 / span 2;
}

/* _____________________________________
   |||||||||||||||||||||||||||||||||||||
   |||| Changing Layout Animation ||||||
   |||||||||||||||||||||||||||||||||||||
*/

.numpad {
  transition: all 0.15s;
}

/* simply using display:none; doesn't work because 
then we can't use transitions anymore

STEPS FOR REMOVING NUMPAD FROM FULL-SIZE KEYBOARD: */
.hidden--step1 {
  /* 1) Hide it visually */
  opacity: 0;
  /* 2) Make it inaccessible to mouse and keyboard */
  pointer-events: none;
  /* 3) Hide it from screen readers */
  visibility: hidden;

  transition: all 0.15;
}

.hidden--step2 {
  width: 0;
  padding: 0;
}

/* ------------------ FOOTER ------------------ */

.footer {
  flex-grow: 1; /* Allow the footer to grow and fill the remaining space */

  display: flex;
  justify-content: center;
  align-items: end;
  padding: 3.2rem 0 2.4rem; /* Add padding for spacing */
}

.footer-text {
  font-size: 1.6rem;
}

.footer-text svg {
  width: 2.4rem;
  height: 2.4rem;
  transform: translateY(0.5rem);
  fill: var(--color-primary);
}
