using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace diagnosticsTool
{
    public partial class WebKeyboardTesterForm : Form
    {
        private Dictionary<Keys, Button> keyButtons;
        private Dictionary<Keys, bool> keyPressed;
        private Panel keyboardPanel;
        private Panel headerPanel;
        private Panel controlPanel;
        private ComboBox themeComboBox;
        private ComboBox layoutComboBox;
        private Label titleLabel;
        private Label instructionLabel;

        // Theme colors
        private KeyboardTheme currentTheme;
        private KeyboardLayout currentLayout;

        public WebKeyboardTesterForm()
        {
            InitializeComponent();
            InitializeKeyboardTester();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 750); // Increased height
            this.Text = "Advanced Keyboard Tester - Native";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(248, 248, 255);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(1000, 650); // Increased minimum height
            this.Icon = SystemIcons.Application;
            this.KeyPreview = true; // Enable key events for the form
            this.WindowState = FormWindowState.Normal; // Ensure normal window state

            this.ResumeLayout(false);
        }

        // Enums for themes and layouts
        public enum KeyboardTheme
        {
            Retro,
            NavyBlue
        }

        public enum KeyboardLayout
        {
            Full,
            TKL,
            SeventyFivePercent
        }

        // Theme color definitions
        public class ThemeColors
        {
            public Color KeyboardBackground { get; set; }
            public Color KeyboardBorder { get; set; }
            public Color KeyBackground { get; set; }
            public Color KeyText { get; set; }
            public Color KeyShadow { get; set; }
            public Color AccentKeyBackground { get; set; }
            public Color AccentKeyText { get; set; }
            public Color PressedKeyBackground { get; set; }
            public Color PressedKeyText { get; set; }
            public Color HoverKeyBackground { get; set; }
        }

        private void InitializeKeyboardTester()
        {
            keyButtons = new Dictionary<Keys, Button>();
            keyPressed = new Dictionary<Keys, bool>();
            currentTheme = KeyboardTheme.Retro;
            currentLayout = KeyboardLayout.Full;

            CreateHeaderPanel();
            CreateControlPanel();
            CreateKeyboardPanel();
            ApplyTheme();

            // Set up event handlers
            this.KeyDown += OnKeyDown;
            this.KeyUp += OnKeyUp;
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.Transparent
            };

            titleLabel = new Label
            {
                Text = "🎹 Advanced Keyboard Tester",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            instructionLabel = new Label
            {
                Text = "Press any key to test it! Keys will light up when pressed and stay highlighted when tested.",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(127, 140, 141),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            headerPanel.Controls.Add(instructionLabel);
            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreateControlPanel()
        {
            controlPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.Transparent
            };

            // Theme selector
            Label themeLabel = new Label
            {
                Text = "Theme:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(20, 15),
                Size = new Size(50, 20)
            };

            themeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9),
                Location = new Point(75, 12),
                Size = new Size(100, 25)
            };
            themeComboBox.Items.AddRange(new[] { "Retro", "Navy Blue" });
            themeComboBox.SelectedIndex = 0;
            themeComboBox.SelectedIndexChanged += ThemeComboBox_SelectedIndexChanged;

            // Layout selector
            Label layoutLabel = new Label
            {
                Text = "Layout:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(200, 15),
                Size = new Size(50, 20)
            };

            layoutComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9),
                Location = new Point(255, 12),
                Size = new Size(100, 25)
            };
            layoutComboBox.Items.AddRange(new[] { "Full", "TKL", "75%" });
            layoutComboBox.SelectedIndex = 0;
            layoutComboBox.SelectedIndexChanged += LayoutComboBox_SelectedIndexChanged;

            controlPanel.Controls.AddRange(new Control[] { themeLabel, themeComboBox, layoutLabel, layoutComboBox });
            this.Controls.Add(controlPanel);
        }

        private void CreateKeyboardPanel()
        {
            keyboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                AutoScroll = true
            };

            CreateKeyboardLayout();
            this.Controls.Add(keyboardPanel);
        }

        private void CreateKeyboardLayout()
        {
            keyboardPanel.Controls.Clear();
            keyButtons.Clear();

            // Calculate keyboard size based on layout
            int keyboardWidth = currentLayout == KeyboardLayout.Full ? 1150 : 900;
            int keyboardHeight = 450; // Increased height to accommodate all keys

            // Create main keyboard container with proper sizing
            Panel keyboardContainer = new Panel
            {
                Size = new Size(keyboardWidth, keyboardHeight),
                Location = new Point(10, 10), // Reduced margins to fit better
                BackColor = GetThemeColors().KeyboardBackground,
                AutoScroll = false // Disable auto-scroll to prevent issues
            };
            keyboardContainer.Paint += (s, e) => DrawKeyboardBorder(e.Graphics, keyboardContainer.ClientRectangle);

            // Create keyboard sections
            CreateFunctionKeys(keyboardContainer);
            CreateSystemControlKeys(keyboardContainer);
            CreateTypewriterKeys(keyboardContainer);
            CreateNavigationKeys(keyboardContainer);

            if (currentLayout == KeyboardLayout.Full)
            {
                CreateNumpadKeys(keyboardContainer);
            }

            keyboardPanel.Controls.Add(keyboardContainer);

            // Ensure the keyboard panel can accommodate the container
            keyboardPanel.AutoScrollMinSize = new Size(keyboardWidth + 20, keyboardHeight + 20);
        }

        private ThemeColors GetThemeColors()
        {
            switch (currentTheme)
            {
                case KeyboardTheme.Retro:
                    return new ThemeColors
                    {
                        KeyboardBackground = Color.FromArgb(60, 62, 74),
                        KeyboardBorder = Color.FromArgb(48, 54, 66),
                        KeyBackground = Color.FromArgb(248, 249, 250),
                        KeyText = Color.FromArgb(85, 85, 85),
                        KeyShadow = Color.FromArgb(173, 181, 189),
                        AccentKeyBackground = Color.FromArgb(226, 227, 230),
                        AccentKeyText = Color.FromArgb(85, 85, 85),
                        PressedKeyBackground = Color.FromArgb(164, 169, 176),
                        PressedKeyText = Color.FromArgb(8, 14, 66),
                        HoverKeyBackground = Color.FromArgb(220, 221, 225)
                    };
                case KeyboardTheme.NavyBlue:
                    return new ThemeColors
                    {
                        KeyboardBackground = Color.FromArgb(42, 48, 156),
                        KeyboardBorder = Color.FromArgb(33, 38, 126),
                        KeyBackground = Color.FromArgb(61, 72, 155),
                        KeyText = Color.White,
                        KeyShadow = Color.FromArgb(28, 40, 128),
                        AccentKeyBackground = Color.FromArgb(79, 113, 228),
                        AccentKeyText = Color.White,
                        PressedKeyBackground = Color.FromArgb(248, 249, 250),
                        PressedKeyText = Color.FromArgb(85, 85, 85),
                        HoverKeyBackground = Color.FromArgb(71, 82, 175)
                    };
                default:
                    return GetThemeColors(); // Default to Retro
            }
        }

        private Button CreateKeyButton(Keys key, string displayText, Size size, bool isAccent = false)
        {
            var colors = GetThemeColors();

            Button keyButton = new Button
            {
                Text = displayText,
                Size = size,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                TabStop = false,
                Tag = key
            };

            // Apply theme colors
            keyButton.BackColor = isAccent ? colors.AccentKeyBackground : colors.KeyBackground;
            keyButton.ForeColor = isAccent ? colors.AccentKeyText : colors.KeyText;
            keyButton.FlatAppearance.BorderSize = 0;
            keyButton.FlatAppearance.BorderColor = colors.KeyShadow;

            // Add rounded corners
            keyButton.Paint += (s, e) => DrawRoundedKey(e.Graphics, keyButton.ClientRectangle, keyButton.BackColor, keyButton.ForeColor, keyButton.Text);

            // Store button reference
            if (!keyButtons.ContainsKey(key))
            {
                keyButtons[key] = keyButton;
                keyPressed[key] = false;
            }

            return keyButton;
        }

        private void DrawRoundedKey(Graphics g, Rectangle rect, Color backColor, Color textColor, string text)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // Create rounded rectangle path
            using (GraphicsPath path = CreateRoundedRectangle(rect, 8))
            {
                // Fill background
                using (SolidBrush brush = new SolidBrush(backColor))
                {
                    g.FillPath(brush, path);
                }

                // Draw border
                var colors = GetThemeColors();
                using (Pen pen = new Pen(colors.KeyShadow, 1))
                {
                    g.DrawPath(pen, path);
                }
            }

            // Draw text
            using (SolidBrush textBrush = new SolidBrush(textColor))
            {
                StringFormat sf = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(text, new Font("Segoe UI", 9, FontStyle.Bold), textBrush, rect, sf);
            }
        }

        private GraphicsPath CreateRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            int diameter = radius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        private void DrawKeyboardBorder(Graphics g, Rectangle rect)
        {
            var colors = GetThemeColors();
            using (SolidBrush brush = new SolidBrush(colors.KeyboardBackground))
            {
                g.FillRectangle(brush, rect);
            }

            using (Pen pen = new Pen(colors.KeyboardBorder, 3))
            {
                g.DrawRectangle(pen, rect);
            }
        }

        private void CreateFunctionKeys(Panel container)
        {
            int startX = 15; // Reduced margin
            int startY = 15; // Reduced margin
            int keySize = 45;
            int spacing = 5;
            int groupSpacing = 15;

            // ESC key
            var escKey = CreateKeyButton(Keys.Escape, "ESC", new Size(keySize, keySize), true);
            escKey.Location = new Point(startX, startY);
            container.Controls.Add(escKey);

            // Function keys F1-F12
            int currentX = startX + keySize + groupSpacing;

            // F1-F4 group
            for (int i = 1; i <= 4; i++)
            {
                var fKey = CreateKeyButton((Keys)Enum.Parse(typeof(Keys), $"F{i}"), $"F{i}", new Size(keySize, keySize));
                fKey.Location = new Point(currentX, startY);
                container.Controls.Add(fKey);
                currentX += keySize + spacing;
            }

            currentX += groupSpacing;

            // F5-F8 group
            for (int i = 5; i <= 8; i++)
            {
                var fKey = CreateKeyButton((Keys)Enum.Parse(typeof(Keys), $"F{i}"), $"F{i}", new Size(keySize, keySize));
                fKey.Location = new Point(currentX, startY);
                container.Controls.Add(fKey);
                currentX += keySize + spacing;
            }

            currentX += groupSpacing;

            // F9-F12 group
            for (int i = 9; i <= 12; i++)
            {
                var fKey = CreateKeyButton((Keys)Enum.Parse(typeof(Keys), $"F{i}"), $"F{i}", new Size(keySize, keySize));
                fKey.Location = new Point(currentX, startY);
                container.Controls.Add(fKey);
                currentX += keySize + spacing;
            }
        }

        private void CreateSystemControlKeys(Panel container)
        {
            int startX = 750;
            int startY = 15; // Reduced margin to match function keys
            int keySize = 45;
            int spacing = 5;

            var printScreenKey = CreateKeyButton(Keys.PrintScreen, "Prt Sc", new Size(keySize, keySize), true);
            printScreenKey.Location = new Point(startX, startY);
            container.Controls.Add(printScreenKey);

            var scrollLockKey = CreateKeyButton(Keys.Scroll, "Scr Lk", new Size(keySize, keySize), true);
            scrollLockKey.Location = new Point(startX + keySize + spacing, startY);
            container.Controls.Add(scrollLockKey);

            var pauseKey = CreateKeyButton(Keys.Pause, "Pause", new Size(keySize, keySize), true);
            pauseKey.Location = new Point(startX + (keySize + spacing) * 2, startY);
            container.Controls.Add(pauseKey);
        }

        private void CreateTypewriterKeys(Panel container)
        {
            int startX = 15; // Reduced margin to match function keys
            int startY = 75; // Slightly reduced spacing from function keys
            int keySize = 45;
            int spacing = 5;

            // Row 1: Number row
            CreateNumberRow(container, startX, startY, keySize, spacing);

            // Row 2: QWERTY row
            CreateQwertyRow(container, startX, startY + keySize + spacing, keySize, spacing);

            // Row 3: ASDF row
            CreateAsdfRow(container, startX, startY + (keySize + spacing) * 2, keySize, spacing);

            // Row 4: ZXCV row
            CreateZxcvRow(container, startX, startY + (keySize + spacing) * 3, keySize, spacing);

            // Row 5: Bottom row (Ctrl, Win, Alt, Space, etc.)
            CreateBottomRow(container, startX, startY + (keySize + spacing) * 4, keySize, spacing);
        }

        private void CreateNumberRow(Panel container, int startX, int startY, int keySize, int spacing)
        {
            int currentX = startX;

            // Backtick/Tilde key
            var backtickKey = CreateKeyButton(Keys.Oemtilde, "~\n`", new Size(keySize, keySize), true);
            backtickKey.Location = new Point(currentX, startY);
            container.Controls.Add(backtickKey);
            currentX += keySize + spacing;

            // Number keys 1-9, 0
            string[] numberLabels = { "1", "2", "3", "4", "5", "6", "7", "8", "9", "0" };
            Keys[] numberKeys = { Keys.D1, Keys.D2, Keys.D3, Keys.D4, Keys.D5, Keys.D6, Keys.D7, Keys.D8, Keys.D9, Keys.D0 };

            for (int i = 0; i < numberLabels.Length; i++)
            {
                var numberKey = CreateKeyButton(numberKeys[i], numberLabels[i], new Size(keySize, keySize));
                numberKey.Location = new Point(currentX, startY);
                container.Controls.Add(numberKey);
                currentX += keySize + spacing;
            }

            // Minus key
            var minusKey = CreateKeyButton(Keys.OemMinus, "-\n_", new Size(keySize, keySize));
            minusKey.Location = new Point(currentX, startY);
            container.Controls.Add(minusKey);
            currentX += keySize + spacing;

            // Equals key
            var equalsKey = CreateKeyButton(Keys.Oemplus, "+\n=", new Size(keySize, keySize));
            equalsKey.Location = new Point(currentX, startY);
            container.Controls.Add(equalsKey);
            currentX += keySize + spacing;

            // Backspace key
            var backspaceKey = CreateKeyButton(Keys.Back, "Backspace", new Size(keySize * 2, keySize), true);
            backspaceKey.Location = new Point(currentX, startY);
            container.Controls.Add(backspaceKey);
        }

        private void CreateQwertyRow(Panel container, int startX, int startY, int keySize, int spacing)
        {
            int currentX = startX;

            // Tab key
            var tabKey = CreateKeyButton(Keys.Tab, "Tab", new Size((int)(keySize * 1.5), keySize), true);
            tabKey.Location = new Point(currentX, startY);
            container.Controls.Add(tabKey);
            currentX += (int)(keySize * 1.5) + spacing;

            // QWERTY keys
            string qwertyRow = "QWERTYUIOP";
            Keys[] qwertyKeys = { Keys.Q, Keys.W, Keys.E, Keys.R, Keys.T, Keys.Y, Keys.U, Keys.I, Keys.O, Keys.P };

            for (int i = 0; i < qwertyRow.Length; i++)
            {
                var letterKey = CreateKeyButton(qwertyKeys[i], qwertyRow[i].ToString(), new Size(keySize, keySize));
                letterKey.Location = new Point(currentX, startY);
                container.Controls.Add(letterKey);
                currentX += keySize + spacing;
            }

            // Left bracket key
            var leftBracketKey = CreateKeyButton(Keys.OemOpenBrackets, "{\n[", new Size(keySize, keySize));
            leftBracketKey.Location = new Point(currentX, startY);
            container.Controls.Add(leftBracketKey);
            currentX += keySize + spacing;

            // Right bracket key
            var rightBracketKey = CreateKeyButton(Keys.OemCloseBrackets, "}\n]", new Size(keySize, keySize));
            rightBracketKey.Location = new Point(currentX, startY);
            container.Controls.Add(rightBracketKey);
            currentX += keySize + spacing;

            // Backslash key
            var backslashKey = CreateKeyButton(Keys.OemPipe, "|\n\\", new Size((int)(keySize * 1.5), keySize), true);
            backslashKey.Location = new Point(currentX, startY);
            container.Controls.Add(backslashKey);
        }

        private void CreateAsdfRow(Panel container, int startX, int startY, int keySize, int spacing)
        {
            int currentX = startX;

            // Caps Lock key
            var capsKey = CreateKeyButton(Keys.CapsLock, "Caps", new Size((int)(keySize * 1.8), keySize), true);
            capsKey.Location = new Point(currentX, startY);
            container.Controls.Add(capsKey);
            currentX += (int)(keySize * 1.8) + spacing;

            // ASDF keys
            string asdfRow = "ASDFGHJKL";
            Keys[] asdfKeys = { Keys.A, Keys.S, Keys.D, Keys.F, Keys.G, Keys.H, Keys.J, Keys.K, Keys.L };

            for (int i = 0; i < asdfRow.Length; i++)
            {
                var letterKey = CreateKeyButton(asdfKeys[i], asdfRow[i].ToString(), new Size(keySize, keySize));
                letterKey.Location = new Point(currentX, startY);
                container.Controls.Add(letterKey);
                currentX += keySize + spacing;
            }

            // Semicolon key
            var semicolonKey = CreateKeyButton(Keys.OemSemicolon, ":\n;", new Size(keySize, keySize));
            semicolonKey.Location = new Point(currentX, startY);
            container.Controls.Add(semicolonKey);
            currentX += keySize + spacing;

            // Quote key
            var quoteKey = CreateKeyButton(Keys.OemQuotes, "\"\n'", new Size(keySize, keySize));
            quoteKey.Location = new Point(currentX, startY);
            container.Controls.Add(quoteKey);
            currentX += keySize + spacing;

            // Enter key
            var enterKey = CreateKeyButton(Keys.Enter, "Enter", new Size((int)(keySize * 2.3), keySize), true);
            enterKey.Location = new Point(currentX, startY);
            container.Controls.Add(enterKey);
        }

        private void CreateZxcvRow(Panel container, int startX, int startY, int keySize, int spacing)
        {
            int currentX = startX;

            // Left Shift key
            var leftShiftKey = CreateKeyButton(Keys.LShiftKey, "Shift", new Size((int)(keySize * 2.3), keySize), true);
            leftShiftKey.Location = new Point(currentX, startY);
            container.Controls.Add(leftShiftKey);
            currentX += (int)(keySize * 2.3) + spacing;

            // ZXCV keys
            string zxcvRow = "ZXCVBNM";
            Keys[] zxcvKeys = { Keys.Z, Keys.X, Keys.C, Keys.V, Keys.B, Keys.N, Keys.M };

            for (int i = 0; i < zxcvRow.Length; i++)
            {
                var letterKey = CreateKeyButton(zxcvKeys[i], zxcvRow[i].ToString(), new Size(keySize, keySize));
                letterKey.Location = new Point(currentX, startY);
                container.Controls.Add(letterKey);
                currentX += keySize + spacing;
            }

            // Comma key
            var commaKey = CreateKeyButton(Keys.Oemcomma, "<\n,", new Size(keySize, keySize));
            commaKey.Location = new Point(currentX, startY);
            container.Controls.Add(commaKey);
            currentX += keySize + spacing;

            // Period key
            var periodKey = CreateKeyButton(Keys.OemPeriod, ">\n.", new Size(keySize, keySize));
            periodKey.Location = new Point(currentX, startY);
            container.Controls.Add(periodKey);
            currentX += keySize + spacing;

            // Slash key
            var slashKey = CreateKeyButton(Keys.OemQuestion, "?\n/", new Size(keySize, keySize));
            slashKey.Location = new Point(currentX, startY);
            container.Controls.Add(slashKey);
            currentX += keySize + spacing;

            // Right Shift key
            var rightShiftKey = CreateKeyButton(Keys.RShiftKey, "Shift", new Size((int)(keySize * 2.8), keySize), true);
            rightShiftKey.Location = new Point(currentX, startY);
            container.Controls.Add(rightShiftKey);
        }

        private void CreateBottomRow(Panel container, int startX, int startY, int keySize, int spacing)
        {
            int currentX = startX;

            // Left Ctrl key
            var leftCtrlKey = CreateKeyButton(Keys.LControlKey, "Ctrl", new Size((int)(keySize * 1.3), keySize), true);
            leftCtrlKey.Location = new Point(currentX, startY);
            container.Controls.Add(leftCtrlKey);
            currentX += (int)(keySize * 1.3) + spacing;

            // Left Win key
            var leftWinKey = CreateKeyButton(Keys.LWin, "Win", new Size((int)(keySize * 1.3), keySize), true);
            leftWinKey.Location = new Point(currentX, startY);
            container.Controls.Add(leftWinKey);
            currentX += (int)(keySize * 1.3) + spacing;

            // Left Alt key
            var leftAltKey = CreateKeyButton(Keys.LMenu, "Alt", new Size((int)(keySize * 1.3), keySize), true);
            leftAltKey.Location = new Point(currentX, startY);
            container.Controls.Add(leftAltKey);
            currentX += (int)(keySize * 1.3) + spacing;

            // Space key
            var spaceKey = CreateKeyButton(Keys.Space, "Space", new Size((int)(keySize * 6.4), keySize), true);
            spaceKey.Location = new Point(currentX, startY);
            container.Controls.Add(spaceKey);
            currentX += (int)(keySize * 6.4) + spacing;

            // Right Alt key
            var rightAltKey = CreateKeyButton(Keys.RMenu, "Alt", new Size((int)(keySize * 1.3), keySize), true);
            rightAltKey.Location = new Point(currentX, startY);
            container.Controls.Add(rightAltKey);
            currentX += (int)(keySize * 1.3) + spacing;

            // Right Win key (if not 75% layout)
            if (currentLayout != KeyboardLayout.SeventyFivePercent)
            {
                var rightWinKey = CreateKeyButton(Keys.RWin, "Win", new Size((int)(keySize * 1.3), keySize), true);
                rightWinKey.Location = new Point(currentX, startY);
                container.Controls.Add(rightWinKey);
                currentX += (int)(keySize * 1.3) + spacing;
            }

            // Menu key
            var menuKey = CreateKeyButton(Keys.Apps, "Menu", new Size((int)(keySize * 1.3), keySize), true);
            menuKey.Location = new Point(currentX, startY);
            container.Controls.Add(menuKey);
            currentX += (int)(keySize * 1.3) + spacing;

            // Right Ctrl key
            var rightCtrlKey = CreateKeyButton(Keys.RControlKey, "Ctrl", new Size((int)(keySize * 1.3), keySize), true);
            rightCtrlKey.Location = new Point(currentX, startY);
            container.Controls.Add(rightCtrlKey);
        }

        private void CreateNavigationKeys(Panel container)
        {
            int startX = 750;
            int startY = 75; // Match typewriter keys positioning
            int keySize = 45;
            int spacing = 5;

            // Top row navigation keys
            var insertKey = CreateKeyButton(Keys.Insert, "Insert", new Size(keySize, keySize), true);
            insertKey.Location = new Point(startX, startY);
            container.Controls.Add(insertKey);

            var homeKey = CreateKeyButton(Keys.Home, "Home", new Size(keySize, keySize), true);
            homeKey.Location = new Point(startX + keySize + spacing, startY);
            container.Controls.Add(homeKey);

            var pageUpKey = CreateKeyButton(Keys.PageUp, "Pg Up", new Size(keySize, keySize), true);
            pageUpKey.Location = new Point(startX + (keySize + spacing) * 2, startY);
            container.Controls.Add(pageUpKey);

            // Bottom row navigation keys
            var deleteKey = CreateKeyButton(Keys.Delete, "Delete", new Size(keySize, keySize), true);
            deleteKey.Location = new Point(startX, startY + keySize + spacing);
            container.Controls.Add(deleteKey);

            var endKey = CreateKeyButton(Keys.End, "End", new Size(keySize, keySize), true);
            endKey.Location = new Point(startX + keySize + spacing, startY + keySize + spacing);
            container.Controls.Add(endKey);

            var pageDownKey = CreateKeyButton(Keys.PageDown, "Pg Dn", new Size(keySize, keySize), true);
            pageDownKey.Location = new Point(startX + (keySize + spacing) * 2, startY + keySize + spacing);
            container.Controls.Add(pageDownKey);

            // Arrow keys
            int arrowStartY = startY + (keySize + spacing) * 3;

            var upArrowKey = CreateKeyButton(Keys.Up, "↑", new Size(keySize, keySize));
            upArrowKey.Location = new Point(startX + keySize + spacing, arrowStartY);
            container.Controls.Add(upArrowKey);

            var leftArrowKey = CreateKeyButton(Keys.Left, "←", new Size(keySize, keySize));
            leftArrowKey.Location = new Point(startX, arrowStartY + keySize + spacing);
            container.Controls.Add(leftArrowKey);

            var downArrowKey = CreateKeyButton(Keys.Down, "↓", new Size(keySize, keySize));
            downArrowKey.Location = new Point(startX + keySize + spacing, arrowStartY + keySize + spacing);
            container.Controls.Add(downArrowKey);

            var rightArrowKey = CreateKeyButton(Keys.Right, "→", new Size(keySize, keySize));
            rightArrowKey.Location = new Point(startX + (keySize + spacing) * 2, arrowStartY + keySize + spacing);
            container.Controls.Add(rightArrowKey);
        }

        private void CreateNumpadKeys(Panel container)
        {
            int startX = 900;
            int startY = 75; // Match other sections positioning
            int keySize = 45;
            int spacing = 5;

            // Top row
            var numLockKey = CreateKeyButton(Keys.NumLock, "NumLk", new Size(keySize, keySize), true);
            numLockKey.Location = new Point(startX, startY);
            container.Controls.Add(numLockKey);

            var divideKey = CreateKeyButton(Keys.Divide, "/", new Size(keySize, keySize));
            divideKey.Location = new Point(startX + keySize + spacing, startY);
            container.Controls.Add(divideKey);

            var multiplyKey = CreateKeyButton(Keys.Multiply, "*", new Size(keySize, keySize));
            multiplyKey.Location = new Point(startX + (keySize + spacing) * 2, startY);
            container.Controls.Add(multiplyKey);

            var subtractKey = CreateKeyButton(Keys.Subtract, "-", new Size(keySize, keySize));
            subtractKey.Location = new Point(startX + (keySize + spacing) * 3, startY);
            container.Controls.Add(subtractKey);

            // Second row
            int row2Y = startY + keySize + spacing;
            for (int i = 7; i <= 9; i++)
            {
                var numKey = CreateKeyButton((Keys)Enum.Parse(typeof(Keys), $"NumPad{i}"), i.ToString(), new Size(keySize, keySize));
                numKey.Location = new Point(startX + (keySize + spacing) * (i - 7), row2Y);
                container.Controls.Add(numKey);
            }

            var addKey = CreateKeyButton(Keys.Add, "+", new Size(keySize, (keySize * 2) + spacing));
            addKey.Location = new Point(startX + (keySize + spacing) * 3, row2Y);
            container.Controls.Add(addKey);

            // Third row
            int row3Y = startY + (keySize + spacing) * 2;
            for (int i = 4; i <= 6; i++)
            {
                var numKey = CreateKeyButton((Keys)Enum.Parse(typeof(Keys), $"NumPad{i}"), i.ToString(), new Size(keySize, keySize));
                numKey.Location = new Point(startX + (keySize + spacing) * (i - 4), row3Y);
                container.Controls.Add(numKey);
            }

            // Fourth row
            int row4Y = startY + (keySize + spacing) * 3;
            for (int i = 1; i <= 3; i++)
            {
                var numKey = CreateKeyButton((Keys)Enum.Parse(typeof(Keys), $"NumPad{i}"), i.ToString(), new Size(keySize, keySize));
                numKey.Location = new Point(startX + (keySize + spacing) * (i - 1), row4Y);
                container.Controls.Add(numKey);
            }

            var enterKey = CreateKeyButton(Keys.Enter, "Enter", new Size(keySize, (keySize * 2) + spacing));
            enterKey.Location = new Point(startX + (keySize + spacing) * 3, row4Y);
            container.Controls.Add(enterKey);

            // Fifth row
            int row5Y = startY + (keySize + spacing) * 4;
            var zeroKey = CreateKeyButton(Keys.NumPad0, "0", new Size((keySize * 2) + spacing, keySize));
            zeroKey.Location = new Point(startX, row5Y);
            container.Controls.Add(zeroKey);

            var decimalKey = CreateKeyButton(Keys.Decimal, ".", new Size(keySize, keySize));
            decimalKey.Location = new Point(startX + (keySize * 2) + (spacing * 2), row5Y);
            container.Controls.Add(decimalKey);
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (keyButtons.ContainsKey(e.KeyCode))
            {
                var button = keyButtons[e.KeyCode];
                var colors = GetThemeColors();

                // Apply pressed state
                button.BackColor = colors.HoverKeyBackground;
                keyPressed[e.KeyCode] = true;

                // Animate the key press
                AnimateKeyPress(button, true);
            }
        }

        private void OnKeyUp(object sender, KeyEventArgs e)
        {
            if (keyButtons.ContainsKey(e.KeyCode))
            {
                var button = keyButtons[e.KeyCode];
                var colors = GetThemeColors();

                // Set to pressed state (stays highlighted)
                button.BackColor = colors.PressedKeyBackground;
                button.ForeColor = colors.PressedKeyText;

                // Animate the key release
                AnimateKeyPress(button, false);
            }
        }

        private void AnimateKeyPress(Button button, bool pressed)
        {
            // Simple color-based animation instead of size changes to avoid shrinking
            var colors = GetThemeColors();

            if (pressed)
            {
                // Immediate visual feedback with darker color
                button.BackColor = Color.FromArgb(
                    Math.Max(0, colors.HoverKeyBackground.R - 30),
                    Math.Max(0, colors.HoverKeyBackground.G - 30),
                    Math.Max(0, colors.HoverKeyBackground.B - 30)
                );
            }
            else
            {
                // Brief flash effect then set to pressed state
                System.Windows.Forms.Timer flashTimer = new System.Windows.Forms.Timer();
                flashTimer.Interval = 100;
                int flashCount = 0;

                flashTimer.Tick += (s, e) =>
                {
                    flashCount++;
                    if (flashCount == 1)
                    {
                        // Flash bright
                        button.BackColor = Color.FromArgb(
                            Math.Min(255, colors.PressedKeyBackground.R + 40),
                            Math.Min(255, colors.PressedKeyBackground.G + 40),
                            Math.Min(255, colors.PressedKeyBackground.B + 40)
                        );
                    }
                    else
                    {
                        // Set final pressed state
                        button.BackColor = colors.PressedKeyBackground;
                        button.ForeColor = colors.PressedKeyText;
                        flashTimer.Stop();
                        flashTimer.Dispose();
                    }
                };

                flashTimer.Start();
            }
        }

        private void ThemeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            currentTheme = (KeyboardTheme)themeComboBox.SelectedIndex;
            ApplyTheme();
        }

        private void LayoutComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            currentLayout = (KeyboardLayout)layoutComboBox.SelectedIndex;
            CreateKeyboardLayout();
        }

        private void ApplyTheme()
        {
            var colors = GetThemeColors();

            // Update form background
            this.BackColor = Color.FromArgb(248, 248, 255);

            // Update all key buttons
            foreach (var kvp in keyButtons)
            {
                var button = kvp.Value;
                var key = kvp.Key;

                if (keyPressed.ContainsKey(key) && keyPressed[key])
                {
                    // Keep pressed state
                    button.BackColor = colors.PressedKeyBackground;
                    button.ForeColor = colors.PressedKeyText;
                }
                else
                {
                    // Reset to normal state
                    bool isAccent = IsAccentKey(key);
                    button.BackColor = isAccent ? colors.AccentKeyBackground : colors.KeyBackground;
                    button.ForeColor = isAccent ? colors.AccentKeyText : colors.KeyText;
                }

                button.Invalidate(); // Force redraw
            }

            // Update keyboard container
            if (keyboardPanel?.Controls.Count > 0)
            {
                var container = keyboardPanel.Controls[0];
                container.BackColor = colors.KeyboardBackground;
                container.Invalidate();
            }
        }

        private bool IsAccentKey(Keys key)
        {
            Keys[] accentKeys = {
                Keys.Escape, Keys.PrintScreen, Keys.Scroll, Keys.Pause,
                Keys.Oemtilde, Keys.Back, Keys.Tab, Keys.OemPipe,
                Keys.CapsLock, Keys.Enter, Keys.LShiftKey, Keys.RShiftKey,
                Keys.LControlKey, Keys.LWin, Keys.LMenu, Keys.Space,
                Keys.RMenu, Keys.RWin, Keys.Apps, Keys.RControlKey,
                Keys.Insert, Keys.Home, Keys.PageUp, Keys.Delete,
                Keys.End, Keys.PageDown, Keys.NumLock
            };

            return accentKeys.Contains(key);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);
        }
    }
}
