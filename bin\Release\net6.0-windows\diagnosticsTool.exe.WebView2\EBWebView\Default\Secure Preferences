{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "8BC3754EE40FCAE00E1F4CEAB1EC01B97E4BEA21DDCF8AF5BED2CDAD10E48AF8"}, "default_search_provider_data": {"template_url_data": "38F9B05E945DCE52D7DD4B16364960ECF1C3DD0670E311DD31BB8BAE1E80A4A0"}, "edge": {"services": {"account_id": "631D035062E26E9FCBDB8A640D957F0ACAF91F865BCC53D3554E84312C9C72EB", "last_username": "C73757B78A2FDD1257BC46228259BFFAA49292099BA71B79843482F3A09A4201"}}, "enterprise_signin": {"policy_recovery_token": "A891A6DD75CC188E2FD158EF64ED1CD29A580CD16DC9D5D4FEEFB2E55FA1AC88"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "E83716A39D7D66988F155393621EEEB4F5AA07AE5740EE32497790F96ACB89EA", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "AD28DDB3BCB92ADE0933D849A0EE770BE937D4AD447BDA63AC8B58CEE86B4B88"}, "ui": {"developer_mode": "6C2D14AFE9916A7B923FA57C0B56244AB5EE93628198DFFD5E2BB48CC658DEA0"}}, "google": {"services": {"last_signed_in_username": "4183FAF0E23BE6B1E8E67A8DDCAF41CBF16979EC355CDD7E26BE999F58AA020C"}}, "homepage": "849E57054F21F9EEEC6D70DA664E730A504A1D5D97F086A89FE8D2418873CF57", "homepage_is_newtabpage": "70A5FC00090EAA37E2FC018496D9E0F08110CB730577620D9690957C7100D1A0", "media": {"cdm": {"origin_data": "70AB91B74BD46E384CDC44A65959162BD28E205F191C73C535A83C2A2FE6883D"}, "storage_id_salt": "E81BD039F9FB0ACCBA59852C1B3C91E25BBBB76ED2253B1CBE881FFDF4C9BB7B"}, "pinned_tabs": "09E05236D8B2167C54EA53992DE6699A0B2608BCA88147BDD6E312D5D2EEF18B", "prefs": {"preference_reset_time": "A3DAB70AC06E97417535C2F2DCBBC6B6C032FEE04F2A4E1B0D013D9DA308F8C9"}, "safebrowsing": {"incidents_sent": "D4AF90A9E1BF0F53D2DB49D961E141FBAC62A1CBD24ECFE6DAC74485ED09A374"}, "search_provider_overrides": "00300167A0E5FFAD0ADCC592ABC3C9761A4C17F06DB4601B59F8553CD063025B", "session": {"restore_on_startup": "731ADC2DABC3C06B040E38ACD13DA4CAA46CCA3D1DC82F034326F13563B1B288", "startup_urls": "717F088C34912D651DD978C2D5B0EE5334D4439C5515FEC8BFB5D0225EB27E05"}}, "super_mac": "4AF03F5C9B9473AF113D3FACF9917C8C1E91BD016E7ED789C92A1E478460EC40"}}